package cn.taken.ad.logic.adv.tianzao;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.jindai.dto.BidClickUrl;
import cn.taken.ad.logic.adv.tianzao.dto.BidRequest;
import cn.taken.ad.logic.adv.tianzao.dto.BidResponse;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.*;
import cn.taken.ad.logic.media.yifan.dto.ResponseVideo;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;

@Component("TIANZAO" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class TianZaoAdvProcessor implements AdvProcessor {
    private static final Logger log = LoggerFactory.getLogger(TianZaoAdvProcessor.class);

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto request, RtbAdvDto advDto) throws Throwable {
        BidRequest.Builder rtb = BidRequest.newBuilder();
        rtb.setId(request.getReqId());
        rtb.setImp(warpTag(request, advDto));
        rtb.setApp(warpApp(request.getApp(), advDto));
        rtb.setDevice(warpDevice(request.getDevice(), request.getNetwork(), request.getGeo()));
        rtb.setUser(warpUser(request.getUser()));
        advDto.setReqObj(rtb);
//        log.info("request:{}", JsonFormat.printer().omittingInsignificantWhitespace().print(rtb));

        byte[] bytes = rtb.build().toByteArray();
        // 请求地址
        HttpResult httpResult = httpClient.postBytes(advDto.getRtburl(), bytes, new Header[]{
                new BasicHeader("Content-Type", "application/x-protobuf;charset=UTF-8"),
                new BasicHeader("Accept-Encoding", "gzip")
        }, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        // 解析响应
        return parseResponse(advDto, request, httpResult, httpClient);
    }

    private RtbResponseDto parseResponse(RtbAdvDto advDto, RtbRequestDto request, HttpResult httpResult, FastHttpClient httpClient) throws Exception {
        byte[] data = httpResult.getData();
        if (null == data || data.length == 0) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        BidResponse takenResp = BidResponse.parseFrom(data);
//        log.info("response:{}", JsonFormat.printer().omittingInsignificantWhitespace().print(takenResp));
        advDto.setRespObj(takenResp);
        if (takenResp.getCode() == 204) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        // 广告信息
        if(!takenResp.hasBid()){
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        BidResponse.Bid takenTag = takenResp.getBid();

        List<TagResponseDto> tags = new ArrayList<>();
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), LogicState.SUCCESS.getDesc(), takenResp.getId(), tags);
        responseDto.setRespId(takenResp.getId());

        TagResponseDto tag = new TagResponseDto();
        tag.setTagInfoId(takenTag.getTagId());
        tag.setTitle(takenTag.getTitle());
        tag.setDesc(takenTag.getDesc());
        tag.setIconUrl(takenTag.getIconUrl());
        tag.setImgUrls(takenTag.getImgUrlsList());
        tag.setClickUrl(takenTag.getLandingUrl());
        tag.setDeepLinkUrl(takenTag.getDeeplink());
        tag.setUniversalLink(takenTag.getUniversalLink());
        if (takenTag.hasBidFloor()) {
            tag.setPrice((double) takenTag.getBidFloor());
        }
        if (takenTag.hasCType()) {
            switch (takenTag.getCType()) {
                case 1:
                    tag.setMaterialType(MaterialType.IMAGE_TEXT);
                    break;
                case 2:
                    tag.setMaterialType(MaterialType.TEXT);
                    break;
                case 3:
                    tag.setMaterialType(MaterialType.HTML);
                    break;
                case 4:
                    tag.setMaterialType(MaterialType.VIDEO);
                    break;
            }
        }

        if(takenTag.hasCiType()){
            switch (takenTag.getCiType()){
                case 1:
                    tag.setActionType(ActionType.SYSTEM_BROWSER_H5);
                    break;
                case 2:
                    tag.setActionType(ActionType.DEEPLINK);
                    break;
                case 3:
                    tag.setActionType(ActionType.DOWNLOAD);
                    break;
                case 4:
                    tag.setActionType(ActionType.DOWNLOAD);
                    break;
            }
        }

        if(takenTag.hasApp()){
            BidResponse.Bid.App app = takenTag.getApp();
            ResponseAppDto appDto = new ResponseAppDto();
            appDto.setAppName(app.getName());
            appDto.setPackageName(app.getBundle());
            appDto.setAppSize(app.getSize()*1024L);
            appDto.setAppVersion(app.getVer());
            appDto.setAppPrivacyUrl(app.getPrivacyUrl());
            appDto.setAppPermContent(app.getPermContent());
            appDto.setAppDeveloper(app.getDeveloper());
            tag.setAppInfo(appDto);
        }

        if(takenTag.hasVideo()){
            BidResponse.Bid.Video video = takenTag.getVideo();
            ResponseVideoDto videoDto = new ResponseVideoDto();
            videoDto.setVideoUrl(video.getUrl());
            videoDto.setDuration(video.getDuration());
            videoDto.setVideoSize(video.getSize());
            videoDto.setVideoWidth(video.getVw());
            videoDto.setVideoHeight(video.getVh());
            List<String> coverImgUrls = new ArrayList<>(video.getCoverImgUrlList());
            videoDto.setCoverImgUrls(coverImgUrls);
            videoDto.setCoverWidth(video.getVw());
            videoDto.setCoverHeight(video.getVh());
            videoDto.setEndImgUrls(coverImgUrls);
            videoDto.setEndHtml(video.getEndHtml());
            videoDto.setButtonText(video.getButtonText());
            videoDto.setSkipSeconds(video.getSkipSec());
            videoDto.setClickAble(video.getProLanding());
            videoDto.setAutoLanding(video.getComLanding());
            videoDto.setPrefetch(video.getPrefetch());
            videoDto.setEndButtonText(video.getButtonText());
//            videoDto.setEndIconUrl(video.getEndIconUrl());
//            videoDto.setEndTitle(video.getEndTitle());
//            videoDto.setEndDesc(video.getEndDesc());
            tag.setVideoInfo(videoDto);
        }


        //todo
        tag.setWinNoticeUrls(takenTag.getWinUrlsList());
        tag.setFailNoticeUrls(takenTag.getLoseUrlsList());

        responseDto.getTags().add(tag);

        //tracks
        List<ResponseTrackDto> tracks = new ArrayList<>();
        tag.setTracks(tracks);
        takenTag.getTrackersList().forEach(track -> {
            switch (track.getType()) {
                case 1:
                    tracks.add(new ResponseTrackDto(EventType.EXPOSURE.getType(), track.getUrlsList()));
                    break;
                case 2:
                    tracks.add(new ResponseTrackDto(EventType.CLICK.getType(), track.getUrlsList()));
                    break;
                case 3:
                    tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), track.getUrlsList()));
                    break;
                case 4:
                    tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), track.getUrlsList()));
                    break;
                case 5:
                    tracks.add(new ResponseTrackDto(EventType.INSTALL_BEGIN.getType(), track.getUrlsList()));
                    break;
                case 6:
                    tracks.add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), track.getUrlsList()));
                    break;
                case 7:
                    tracks.add(new ResponseTrackDto(EventType.ACTIVE_APP.getType(), track.getUrlsList()));
                    break;
                case 8:
                    tracks.add(new ResponseTrackDto(EventType.CLOSE_AD.getType(), track.getUrlsList()));
                    break;
                case 16:
                    tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_FAIL.getType(), track.getUrlsList()));
                    break;
                case 17:
                    tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_SUCCESS.getType(), track.getUrlsList()));
                    break;
                case 18:
                    tracks.add(new ResponseTrackDto(EventType.APP_NOT_INSTALL.getType(), track.getUrlsList()));
                    break;
                case 19:
                    tracks.add(new ResponseTrackDto(EventType.APP_INSTALLED.getType(), track.getUrlsList()));
                    break;
                case 20:
                    tracks.add(new ResponseTrackDto(EventType.DEEPLINK_START.getType(), track.getUrlsList()));
                    break;
                case 31:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), track.getUrlsList()));
                    break;
                case 32:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_25.getType(), track.getUrlsList()));
                    break;
                case 33:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_50.getType(), track.getUrlsList()));
                    break;
                case 34:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_75.getType(), track.getUrlsList()));
                    break;
                case 35:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_END.getType(), track.getUrlsList()));
                    break;
                case 36:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_SKIP.getType(), track.getUrlsList()));
                    break;
                case 37:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_FULL_SCREEN.getType(), track.getUrlsList()));
                    break;
                case 38:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_EXITS_FULL_SCREEN.getType(), track.getUrlsList()));
                    break;
                case 39:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_LOADED_SUCCESS.getType(), track.getUrlsList()));
                    break;
                case 40:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_LOADED_FAIL.getType(), track.getUrlsList()));
                    break;
                case 41:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_MUTE.getType(), track.getUrlsList()));
                    break;
                case 42:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_UNMUTE.getType(), track.getUrlsList()));
                    break;
                case 43:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_PAUSE.getType(), track.getUrlsList()));
                    break;
                case 44:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_CONTINUE.getType(), track.getUrlsList()));
                    break;
                case 45:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_PLAY_ERROR.getType(), track.getUrlsList()));
                    break;
                case 46:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_PLAY_REPLAY.getType(), track.getUrlsList()));
                    break;
                case 47:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_SLIDE_UP.getType(), track.getUrlsList()));
                    break;
                case 48:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_SLIDE_DOWN.getType(), track.getUrlsList()));
                    break;
                case 49:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_CLOSE.getType(), track.getUrlsList()));
                    break;
                case 50:
                    tracks.add(new ResponseTrackDto(EventType.VIDEO_CLICK.getType(), track.getUrlsList()));
                    break;
            }
        });

        tag.getTracks().forEach(track -> {
            List<String> urls = track.getTrackUrls();
            urls = replaceMacro("__PRICE__", urls, String.valueOf(tag.getPrice()));
            urls = replaceMacro("__REQ_WIDTH__", urls, MacroType.REQ_WIDTH.getCode());
            urls = replaceMacro("__REQ_HEIGHT__", urls, MacroType.REQ_HEIGHT.getCode());
            urls = replaceMacro("__WIDTH__", urls, MacroType.WIDTH.getCode());
            urls = replaceMacro("__HEIGHT__", urls, MacroType.HEIGHT.getCode());
            urls = replaceMacro("__DOWN_X__", urls, MacroType.DOWN_X.getCode());
            urls = replaceMacro("__DOWN_Y__", urls, MacroType.DOWN_Y.getCode());
            urls = replaceMacro("__UP_X__", urls, MacroType.UP_X.getCode());
            urls = replaceMacro("__UP_Y__", urls, MacroType.UP_Y.getCode());
            urls = replaceMacro("__ABS_DOWN_X__", urls, MacroType.ABS_DOWN_X.getCode());
            urls = replaceMacro("__ABS_DOWN_Y__", urls, MacroType.ABS_DOWN_Y.getCode());
            urls = replaceMacro("__ABS_UP_X__", urls, MacroType.ABS_UP_X.getCode());
            urls = replaceMacro("__ABS_UP_Y__", urls, MacroType.ABS_UP_Y.getCode());
            urls = replaceMacro("__DP_WIDTH__", urls, MacroType.DP_WIDTH.getCode());
            urls = replaceMacro("__DP_HEIGHT__", urls, MacroType.DP_HEIGHT.getCode());
            urls = replaceMacro("__DP_DOWN_X__", urls, MacroType.DP_DOWN_X.getCode());
            urls = replaceMacro("__DP_DOWN_Y__", urls, MacroType.DP_DOWN_Y.getCode());
            urls = replaceMacro("__DP_UP_X__", urls, MacroType.DP_UP_X.getCode());
            urls = replaceMacro("__DP_UP_Y__", urls, MacroType.DP_UP_Y.getCode());
            urls = replaceMacro("__SLD__", urls, MacroType.SLD.getCode());
            urls = replaceMacro("__EVENT_TIME__", urls, MacroType.TIME_SECONDS.getCode());
            urls = replaceMacro("__CLICK_TIME_START__", urls, MacroType.START_TIME_SECONDS.getCode());
            urls = replaceMacro("__CLICK_TIME_END__", urls, MacroType.END_TIME_SECONDS.getCode());
            urls = replaceMacro("__CLICK_ID__", urls, MacroType.CLICK_ID.getCode());
            urls = replaceMacro("__DURATION__", urls, MacroType.VIDEO_TIME.getCode());
            urls = replaceMacro("__PLAY_BEGIN_TIME__", urls, MacroType.VIDEO_BEGIN_TIME.getCode());
            urls = replaceMacro("__PLAY_END_TIME__", urls, MacroType.VIDEO_END_TIME.getCode());
            urls = replaceMacro("__PLAY_FIRST_FRAME__", urls, MacroType.VIDEO_PLAY_FIRST_FRAME.getCode());
            urls = replaceMacro("__PLAY_LAST_FRAME__", urls, MacroType.VIDEO_PLAY_LAST_FRAME.getCode());
            urls = replaceMacro("__PLAY_SCENE__", urls, MacroType.VIDEO_SCENE.getCode());
            urls = replaceMacro("__PLAY_TYPE__", urls, MacroType.VIDEO_TYPE.getCode());
            urls = replaceMacro("__PLAY_STATUS__", urls, MacroType.VIDEO_STATUS.getCode());
            urls = replaceMacro("__TARGET_APP_INSTALL__", urls, MacroType.TARGET_APP_INSTALL.getCode());
            track.setTrackUrls(urls);
        });


        return responseDto;
    }

    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam param) throws Exception {
        if (!param.isBiddingSuccess()) {
            return SuperResult.rightResult();
        }
        boolean success = false;
        for (String url : param.getUrls()) {
            HttpResult result = httpClient.get(url, null, null, 5000);
            if (!result.isSuccess()) {
                log.error("notice fail httpCode:{},Url:{}", result.getStatusLine(), url, result.getThrowable());
            }
            if (!success) {
                success = result.isSuccess();
            }
        }
        return success ? SuperResult.rightResult() : SuperResult.badResult();
    }

    private BidRequest.App warpApp(RequestAppDto dto, RtbAdvDto rtbDto) {
        BidRequest.App.Builder app = BidRequest.App.newBuilder();
//        app.seta(rtbDto.getAppCode() != null ? rtbDto.getAppCode() : "");
        app.setName(dto.getAppName() != null ? dto.getAppName() : "");
        app.setBundle(dto.getBundle() != null ? dto.getBundle() : "");
        app.setVer(dto.getAppVersion() != null ? dto.getAppVersion() : "");
        app.setStoreUrl(dto.getAppstoreUrl() != null ? dto.getAppstoreUrl() : "");
        return app.build();
    }

    private BidRequest.Device warpDevice(RequestDeviceDto deviceDto, RequestNetworkDto network, RequestGeoDto geo) {
        BidRequest.Device.Builder builder = BidRequest.Device.newBuilder();
        builder.setIp(network.getIp() != null ? network.getIp() : "");
        if (StringUtils.isNotEmpty(network.getIpv6())) {
            builder.setIpv6(network.getIpv6());
        }
        builder.setUa(deviceDto.getUserAgent() != null ? deviceDto.getUserAgent() : "");

        //设备OS类型，取值：IOS、Android
        switch (deviceDto.getOsType()) {
            case ANDROID:
                builder.setOs("Android");
                break;
            case IOS:
                builder.setOs("IOS");
                break;
        }

        builder.setOsv(deviceDto.getOsVersion() != null ? deviceDto.getOsVersion() : "");

        //设备类型，0：未知，1：手机，2：平板，3：智能电视；
        switch (deviceDto.getDeviceType()) {
            case PAD:
                builder.setDeviceType(2);
                break;
            case PHONE:
                builder.setDeviceType(1);
                break;
            case TV:
                builder.setDeviceType(3);
                break;
            default:
                builder.setDeviceType(0);

        }

        if (geo != null) {
            builder.setGeo(warpGeo(geo));
        }
        builder.setNetwork(warpNetwork(deviceDto, network));

        builder.setBrand(deviceDto.getBrand() != null ? deviceDto.getBrand() : "");
        builder.setModel(deviceDto.getModel() != null ? deviceDto.getModel() : "");
        builder.setModelCode(deviceDto.getModelCode() != null ? deviceDto.getModelCode() : "");
        builder.setOrientation(deviceDto.getOrientation() != null ? deviceDto.getOrientation().getType() : 0);

        builder.setDw(deviceDto.getWidth() != null ? deviceDto.getWidth() : 0);
        builder.setDh(deviceDto.getHeight() != null ? deviceDto.getHeight() : 0);
        if (deviceDto.getScreenDensity() != null) {
            builder.setDensity(deviceDto.getScreenDensity().doubleValue());
        }
        builder.setPpi(deviceDto.getPpi() != null ? deviceDto.getPpi() : 0);
        builder.setScreenSize(deviceDto.getScreenInch() != null ? deviceDto.getScreenInch().doubleValue() : 0);

        builder.setSerialno(deviceDto.getSerialNO() != null ? deviceDto.getSerialNO() : "");
        builder.setAnId(deviceDto.getAndroidId() != null ? deviceDto.getAndroidId() : "");
        builder.setAnIdMd5(deviceDto.getAndroidIdMd5() != null ? deviceDto.getAndroidIdMd5() : "");
        builder.setImei(deviceDto.getImei() != null ? deviceDto.getImei() : "");
        builder.setImeiMd5(deviceDto.getImeiMd5() != null ? deviceDto.getImeiMd5() : "");
        builder.setOaid(deviceDto.getOaid() != null ? deviceDto.getOaid() : "");
        builder.setOaidMd5(deviceDto.getOaidMd5() != null ? deviceDto.getOaidMd5() : "");
        builder.setApiLevel(deviceDto.getApiLevel() != null ? deviceDto.getApiLevel().toString() : "");
        builder.setPaid(deviceDto.getPaid() != null ? deviceDto.getPaid() : "");

        builder.setIdfa(deviceDto.getIdfa() != null ? deviceDto.getIdfa() : "");
        builder.setIdfaMd5(deviceDto.getIdfaMd5() != null ? deviceDto.getIdfaMd5() : "");
        builder.setOpenUdid(deviceDto.getOpenUdId() != null ? deviceDto.getOpenUdId() : "");
        builder.setDeviceName(deviceDto.getDeviceName() != null ? deviceDto.getDeviceName() : "");
        builder.setDeviceNameMd5(deviceDto.getDeviceNameMd5() != null ? deviceDto.getDeviceNameMd5() : "");

        builder.setLanguage(deviceDto.getLanguage() != null ? deviceDto.getLanguage() : "");
        builder.setCountry(deviceDto.getCountry() != null ? deviceDto.getCountry() : "");
        builder.setRomVer(deviceDto.getRomVersion() != null ? deviceDto.getRomVersion() : "");
        builder.setSysComplingTime(deviceDto.getSysCompileTime() != null ? deviceDto.getSysCompileTime() : "");
        builder.setBootTime(Integer.parseInt(deviceDto.getSysStartTime() != null ? deviceDto.getSysStartTime() : ""));
        builder.setUpdateTime(Integer.parseInt(deviceDto.getSysUpdateTime() != null ? deviceDto.getSysUpdateTime() : ""));
        builder.setInitTime(deviceDto.getSysInitTime() != null ? deviceDto.getSysInitTime() : "");
        int diskSize = deviceDto.getDeviceHardDisk() != null ? deviceDto.getDeviceHardDisk().intValue() / 1024 / 1024 / 1024 : 0;
        builder.setDiskSize(diskSize);
        int memorySize = deviceDto.getDeviceMemory() != null ? deviceDto.getDeviceMemory().intValue() / 1024 / 1024 / 1024 : 0;
        builder.setMemorySize(memorySize);

        builder.setBatteryStatus(deviceDto.getBatteryStatus());
        builder.setBatteryPower(deviceDto.getBatteryPower());
        builder.setCpuNum(deviceDto.getCpuNum());
        builder.setCpuFre(deviceDto.getCpuFreq());
        builder.setTimeZone(deviceDto.getTimeZone() != null ? deviceDto.getTimeZone() : "");
        builder.setLmt(deviceDto.getIdfaPolicy());
//        builder.setLaccu(deviceDto.getLaccu());

        builder.setBootMark(deviceDto.getBootMark() != null ? deviceDto.getBootMark() : "");
        builder.setUpdateMark(deviceDto.getUpdateMark() != null ? deviceDto.getUpdateMark() : "");
        builder.setAppStoreVer(deviceDto.getAppStoreVersion() != null ? deviceDto.getAppStoreVersion() : "");
        builder.setHmsVer(deviceDto.getHmsVersion() != null ? deviceDto.getHmsVersion() : "");

        if (deviceDto.getSkanVersion() != null) {
            deviceDto.getSkanVersion().forEach(v -> {
                if (StringUtils.isNotBlank(v)) {
                    builder.addSkadnetworkVer(v);
                }
            });
        }

        if (deviceDto.getInstalledAppInfo() != null && !deviceDto.getInstalledAppInfo().isEmpty()) {
            deviceDto.getInstalledAppInfo().forEach(app -> {
                if (StringUtils.isNotBlank(app.getAppName())) {
                    builder.addInstalledApp(app.getAppName());
                }
            });
        }
        if (!CollectionUtils.isEmpty(deviceDto.getCaids())) {
            deviceDto.getCaids().forEach(caid -> {
                BidRequest.CaidList.Builder caidList = BidRequest.CaidList.newBuilder();
                caidList.setId(caid.getCaid());
                caidList.setVer(caid.getVersion());
                builder.addCaidList(caidList);
                if (caid.getVendor() != null) {
                    if (caid.getVendor() == CaidVendor.ALI) {
                        builder.setCaidVendor(2);
                    } else if (caid.getVendor() == CaidVendor.RY) {
                        builder.setCaidVendor(0);
                    } else if (caid.getVendor() == CaidVendor.XTY) {
                        builder.setCaidVendor(1);
                    }
                }
            });
        }
        builder.setBootTimeNano(deviceDto.getSysStartTime() != null ? deviceDto.getSysStartTime() : "");
        builder.setUpdateTimeNano(deviceDto.getSysUpdateTime() != null ? deviceDto.getSysUpdateTime() : "");

        return builder.build();
    }

    private BidRequest.Network warpNetwork(RequestDeviceDto device, RequestNetworkDto network) {
        BidRequest.Network.Builder builder = BidRequest.Network.newBuilder();
        if (network.getConnectType() != null) {
            builder.setConType(network.getConnectType().getType());

        }
        if (network.getCarrierType() != null) {
            builder.setCarrier(network.getCarrierType().getType());
            switch (network.getCarrierType()) {
                case CM:
                    builder.setMcc("460");
                    builder.setMnc("00");
                    break;
                case CU:
                    builder.setMcc("461");
                    builder.setMnc("01");
                    break;
                case CT:
                    builder.setMcc("463");
//                    builder.setMnc("00");
                    break;
            }

        }
        if (StringUtils.isNotEmpty(device.getImsi())) {
            builder.setImsi(device.getImsi());
        }
        if (StringUtils.isNotEmpty(device.getImsi())) {
            builder.setImsi(device.getImsi());
        }
        if (StringUtils.isNotEmpty(network.getMac())) {
            builder.setMac(network.getMac());
        }
        if (StringUtils.isNotEmpty(network.getMacMd5())) {
            builder.setMacMd5(network.getMacMd5());
        }
        if (StringUtils.isNotEmpty(network.getSsid())) {
            builder.setSsid(network.getSsid());
        }
        return builder.build();
    }

    private BidRequest.Imp warpTag(RtbRequestDto requestDto, RtbAdvDto dto) {
        BidRequest.Imp.Builder tag = BidRequest.Imp.newBuilder();
        RequestTagDto tagDto = requestDto.getTag();
        tag.setTagId(dto.getTagCode() != null ? dto.getTagCode() : "");
        if (null != tagDto.getWidth()) {
            tag.setW(tagDto.getWidth());
        }
        if (null != tagDto.getHeight()) {
            tag.setH(tagDto.getHeight());
        }
        if (null != dto.getPrice()) {
            tag.setBidFloor(dto.getPrice().intValue());
        }
        return tag.build();
    }


    private BidRequest.Geo warpGeo(RequestGeoDto dto) {
        BidRequest.Geo.Builder geo = BidRequest.Geo.newBuilder();
        if (dto.getCoordinateType() != null) {
            if (Objects.requireNonNull(dto.getCoordinateType()) == CoordinateType.GLOBAL) {
                geo.setType(1);
            }
        }
        if (null != dto.getLatitude()) {
            geo.setLat(dto.getLatitude().floatValue());
        }
        if (null != dto.getLongitude()) {
            geo.setLon(dto.getLongitude().floatValue());
        }
        return geo.build();
    }


    private BidRequest.User warpUser(RequestUserDto dto) {
        BidRequest.User.Builder user = BidRequest.User.newBuilder();
        if (dto.getUserId() != null) {
            user.setId(dto.getUserId());
        }
        if (null != dto.getAge()) {
            user.setYob(String.valueOf(calculateBirthYear(dto.getAge())));
        }
        if (dto.getGender() != null) {
            user.setGender(dto.getGender().equals("F") ? 1 : 2);
        }
        if (dto.getInterest() != null) {
            List<String> ins = new ArrayList<String>(Arrays.asList(dto.getInterest()));
            user.setKeywords(StringUtils.join(ins, ","));
        }
        return user.build();
    }

    public static int calculateBirthYear(int age) {
        int currentYear = LocalDate.now().getYear();
        return currentYear - age;
    }
}
